import { fileURLToPath, URL } from "node:url";
import path from "node:path";
import { defineConfig, loadEnv } from "vite";
import type { ConfigEnv } from "vite";
import { createVitePlugins } from "./build/vite/plugins";
import { wrapperEnv } from "./build/vite/env";
// import { OUTPUT_DIR } from "./build/constant";
import { createProxy } from "./build/vite/proxy";
import {
  getBuildOptimization,
  getOptimizeDeps,
  getCSSOptimization,
} from "./build/vite/optimization";

// https://vite.dev/config/
export default defineConfig(({ mode, command }: ConfigEnv) => {
  const root = process.cwd();
  const envDir = path.join(root, "env");
  const env = loadEnv(mode, envDir, "VITE_");
  const viteEnv = wrapperEnv(env);
  const isBuild = command === "build";
  const outDir = process.env.npm_config_outdir || "dist";

  const { VITE_PORT, VITE_PROXY, VITE_DROP_CONSOLE } = viteEnv;

  // 只在构建时生成打包时间戳
  const defineConfig: Record<string, any> = {};

  if (isBuild) {
    const buildTime = new Date().toLocaleString("zh-CN", {
      timeZone: "Asia/Shanghai",
      // year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
    defineConfig.__BUILD_TIME__ = JSON.stringify(buildTime);
  }

  return {
    base: "/",
    root,
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    define: defineConfig,
    // 优化依赖预构建
    optimizeDeps: getOptimizeDeps(),
    // CSS 相关优化
    css: getCSSOptimization(),
    server: {
      cors: true, // 允许跨域
      host: "0.0.0.0",
      hmr: true, // 确保HMR开启，默认是开启的
      // open: true, // 服务启动时是否自动打开浏览器
      open: `http://localhost:${VITE_PORT}`, // 服务启动时是否自动打开浏览器
      port: Number(VITE_PORT),
      proxy: {
        "/local": {
          target: env.VITE_PROXY_TARGET || "https://dev.nustaronline.vip",
          changeOrigin: true,
          secure: true,
          rewrite: (path) => path.replace(/^\/local/, ""),
        },
      },
    },
    esbuild: {
      // 使用 esbuild 压缩 剔除 console.log
      drop: VITE_DROP_CONSOLE ? (["console", "debugger"] as ("console" | "debugger")[]) : undefined,
      // minify: true, // minify: true, 等于 minify: 'esbuild',
    },
    plugins: createVitePlugins(viteEnv, isBuild),
    build: {
      outDir,
      // 提高 chunk 大小警告限制，避免警告
      chunkSizeWarningLimit: 2000, // 2MB，更宽松的限制
      ...getBuildOptimization(VITE_DROP_CONSOLE),

      // 字体文件处理优化 - 禁用内联让字体文件独立缓存
      // assetsInlineLimit: 0,

      // 忽略特定文件的警告
      rollupOptions: {
        ...getBuildOptimization(VITE_DROP_CONSOLE).rollupOptions,
        onwarn(warning, warn) {
          // 忽略 VConsole 的 eval 警告
          if (warning.code === "EVAL" && warning.id?.includes("vconsole")) {
            return;
          }
          // 忽略其他常见的无害警告
          if (warning.code === "MODULE_LEVEL_DIRECTIVE") {
            return;
          }
          // 其他警告正常显示
          warn(warning);
        },
      },
    },
  };
});
