

# 预发环境变量
VITE_APP_TITLE=Nustar Pay Result (Staging)
VITE_APP_ENV=staging
VITE_PORT=80
VITE_BASE_URL=https://pre.nustaronline.vip
# 静态资源前缀
VITE_ASSETS_URL=https://uat-nustar-static.nustaronline.vip/
VITE_API_TIMEOUT=30000
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_CONSOLE=false
VITE_PROXY_TARGET=https://pre.nustaronline.vip
VITE_JUMP_URL=https://web.nustaronline.vip/?debug_web_login=1

# 接口前缀
VITE_API_URL=https://pre.nustaronline.vip/

# 前端域名地址
VITE_WEB_URL=https://web.nustaronline.vip

# Gcash 小程序商城链接
VITE_GCASH_SHOP_URL=https://gcashdev.page.link/?link=https://gcash.splashscreen/?redirect=gcash%3A%2F%2Fcom.mynt.gcash%2Fapp%2F006300121300%3FappId%3D2170020216334562%2526page%253Dpages%252Fgame%252Flist%2526apn%253Dcom.globe.gcash.android.uat%2526isi%253D1358216762%2526ibi%253Dxyz.mynt.gcashdev&apn=com.globe.gcash.android.uat&ibi=xyz.mynt.gcashdev

# 跨域代理，可以配置多个，请注意不要换行
VITE_PROXY=[["/mock-api","http://localhost:8001"]]

# 是否删除console
VITE_DROP_CONSOLE=false

# 是否启用 VConsole 调试工具
VITE_ENABLE_VCONSOLE=true
